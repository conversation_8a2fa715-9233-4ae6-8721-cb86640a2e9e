require('dotenv').config();
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Sample shop policies data
const samplePolicies = [
  {
    title: "Chính sách vận chuyển",
    content: `
**CHÍNH SÁCH VẬN CHUYỂN**

1. **Phạm vi giao hàng:**
   - Giao hàng toàn quốc
   - Ưu tiên khu vực nội thành các thành phố lớn

2. **Thời gian giao hàng:**
   - N<PERSON>i thành: 1-2 ngày làm việc
   - Ng<PERSON><PERSON>i thành: 2-3 ngày làm việc
   - Tỉnh xa: 3-5 ngày làm việc

3. **Phí vận chuyển:**
   - Miễn phí giao hàng cho đơn hàng từ 500.000đ
   - Phí giao hàng nội thành: 30.000đ
   - <PERSON><PERSON> giao hàng ngoại thành: 50.000đ

4. **L<PERSON>u ý:**
   - <PERSON><PERSON>n phẩm được đóng gói cẩn thận, b<PERSON><PERSON> quản lạnh
   - Kh<PERSON>ch hàng vui lòng kiểm tra hàng khi nhận
    `,
    type: "shipping"
  },
  {
    title: "Chính sách khuyến mãi",
    content: `
**CHÍNH SÁCH KHUYẾN MÃI**

1. **Khuyến mãi thành viên:**
   - Thành viên mới: Giảm 10% đơn hàng đầu tiên
   - Thành viên VIP: Giảm 15% mọi đơn hàng
   - Tích điểm: 1% giá trị đơn hàng

2. **Khuyến mãi theo mùa:**
   - Tết Nguyên Đán: Giảm 20%
   - Trung Thu: Giảm 15%
   - Black Friday: Giảm 25%

3. **Khuyến mãi số lượng:**
   - Mua 2 tặng 1 (áp dụng sản phẩm cùng loại)
   - Mua từ 5 sản phẩm: Giảm 10%
   - Mua từ 10 sản phẩm: Giảm 20%

4. **Điều kiện:**
   - Không áp dụng đồng thời nhiều chương trình
   - Khuyến mãi có thể thay đổi theo thời gian
    `,
    type: "promotion"
  },
  {
    title: "Chính sách thanh toán",
    content: `
**CHÍNH SÁCH THANH TOÁN**

1. **Hình thức thanh toán:**
   - Thanh toán khi nhận hàng (COD)
   - Chuyển khoản ngân hàng
   - Ví điện tử (MoMo, ZaloPay)
   - Thẻ tín dụng/ghi nợ

2. **Thông tin chuyển khoản:**
   - Ngân hàng: Vietcombank
   - Số tài khoản: **********
   - Chủ tài khoản: CÔNG TY ABC
   - Nội dung: Họ tên + Số điện thoại

3. **Lưu ý:**
   - Đơn hàng được xác nhận sau khi thanh toán
   - Hóa đơn VAT theo yêu cầu
   - Bảo mật thông tin thanh toán 100%
    `,
    type: "payment"
  },
  {
    title: "Chính sách đổi trả",
    content: `
**CHÍNH SÁCH ĐỔI TRẢ**

1. **Điều kiện đổi trả:**
   - Sản phẩm còn nguyên vẹn, chưa sử dụng
   - Trong vòng 7 ngày kể từ ngày nhận hàng
   - Có hóa đơn mua hàng

2. **Trường hợp được đổi trả:**
   - Sản phẩm bị lỗi do nhà sản xuất
   - Giao sai sản phẩm
   - Sản phẩm hết hạn sử dụng
   - Không đúng mô tả

3. **Quy trình đổi trả:**
   - Liên hệ hotline: 1900-xxxx
   - Gửi ảnh sản phẩm lỗi
   - Đóng gói và gửi trả
   - Hoàn tiền trong 3-5 ngày

4. **Chi phí:**
   - Miễn phí đổi trả nếu lỗi từ shop
   - Khách hàng chịu phí ship nếu đổi ý
    `,
    type: "return"
  },
  {
    title: "Chính sách bảo mật",
    content: `
**CHÍNH SÁCH BẢO MẬT**

1. **Thu thập thông tin:**
   - Chỉ thu thập thông tin cần thiết
   - Có sự đồng ý của khách hàng
   - Bảo mật tuyệt đối

2. **Sử dụng thông tin:**
   - Xử lý đơn hàng
   - Chăm sóc khách hàng
   - Gửi thông tin khuyến mãi (nếu đồng ý)

3. **Bảo vệ thông tin:**
   - Mã hóa dữ liệu
   - Hệ thống bảo mật cao
   - Không chia sẻ với bên thứ 3

4. **Quyền của khách hàng:**
   - Yêu cầu xóa thông tin
   - Cập nhật thông tin cá nhân
   - Từ chối nhận thông tin quảng cáo
    `,
    type: "privacy"
  },
  {
    title: "Điều khoản sử dụng",
    content: `
**ĐIỀU KHOẢN SỬ DỤNG**

1. **Quy định chung:**
   - Tuân thủ pháp luật Việt Nam
   - Sử dụng dịch vụ đúng mục đích
   - Không vi phạm quyền lợi người khác

2. **Trách nhiệm người dùng:**
   - Cung cấp thông tin chính xác
   - Bảo mật tài khoản cá nhân
   - Thanh toán đúng hạn

3. **Trách nhiệm của shop:**
   - Cung cấp sản phẩm chất lượng
   - Giao hàng đúng hạn
   - Hỗ trợ khách hàng 24/7

4. **Xử lý tranh chấp:**
   - Ưu tiên thương lượng
   - Trọng tài nếu cần thiết
   - Tuân thủ quyết định pháp luật
    `,
    type: "terms"
  }
];

async function importShopPolicies() {
  try {
    console.log('🏪 Starting shop policies import...\n');

    for (let i = 0; i < samplePolicies.length; i++) {
      const policyData = samplePolicies[i];
      
      try {
        // Check if policy of this type already exists
        const existingPolicy = await prisma.shopPolicy.findFirst({
          where: { 
            type: policyData.type,
            title: policyData.title 
          }
        });

        if (existingPolicy) {
          console.log(`⚠️ Policy "${policyData.title}" (${policyData.type}) already exists, skipping...`);
          continue;
        }

        const policy = await prisma.shopPolicy.create({
          data: {
            title: policyData.title,
            content: policyData.content.trim(),
            type: policyData.type
          }
        });

        console.log(`✅ Created policy: ${policy.title} (${policy.type}) - ID: ${policy.id}`);
      } catch (error) {
        console.error(`❌ Error creating policy ${i + 1}:`, error.message);
      }
    }

    console.log('\n✅ Shop policies import completed successfully!');
    
    // Show summary
    const totalPolicies = await prisma.shopPolicy.count();
    const policyTypes = await prisma.shopPolicy.findMany({
      select: { type: true },
      distinct: ['type']
    });
    
    console.log(`\n📊 Summary:`);
    console.log(`   Total policies: ${totalPolicies}`);
    console.log(`   Policy types: ${policyTypes.map(p => p.type).join(', ')}`);
    
  } catch (error) {
    console.error('❌ Import failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the import
importShopPolicies();
