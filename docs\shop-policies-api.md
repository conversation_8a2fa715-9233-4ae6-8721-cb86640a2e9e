# Shop Policies API Documentation

## Base URL
```
http://localhost:3000/api/shop-policies
```

## Policy Types
The API supports the following policy types:
- `shipping` - Shipping policies
- `promotion` - Promotion and discount policies  
- `payment` - Payment policies
- `return` - Return and refund policies
- `privacy` - Privacy policies
- `terms` - Terms of service
- `warranty` - Warranty policies
- `refund` - Refund policies

## Endpoints

### 1. Get All Shop Policies
**GET** `/api/shop-policies`

Get a paginated list of all shop policies with optional filtering and search.

#### Query Parameters
- `page` (optional): Page number (default: 1)
- `limit` (optional): Number of items per page (default: 10)
- `type` (optional): Filter by policy type
- `search` (optional): Search term to filter policies by title, content, or type
- `sortBy` (optional): Field to sort by (default: 'createdAt')
- `sortOrder` (optional): Sort order 'asc' or 'desc' (default: 'desc')

#### Example Request
```bash
GET /api/shop-policies?page=1&limit=5&type=shipping&search=giao&sortBy=title&sortOrder=asc
```

#### Example Response
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "Chính sách vận chuyển",
      "content": "Detailed shipping policy content...",
      "type": "shipping",
      "createdAt": "2025-01-24T10:00:00.000Z",
      "updatedAt": "2025-01-24T10:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 5,
    "total": 1,
    "pages": 1
  }
}
```

### 2. Get Policy Types
**GET** `/api/shop-policies/types`

Get information about available policy types.

#### Example Response
```json
{
  "success": true,
  "data": {
    "validTypes": ["shipping", "promotion", "payment", "return", "privacy", "terms", "warranty", "refund"],
    "usedTypes": ["shipping", "payment", "return"],
    "availableTypes": ["promotion", "privacy", "terms", "warranty", "refund"]
  }
}
```

### 3. Get Single Policy
**GET** `/api/shop-policies/:id`

Get a specific shop policy by ID.

#### Example Request
```bash
GET /api/shop-policies/1
```

#### Example Response
```json
{
  "success": true,
  "data": {
    "id": 1,
    "title": "Chính sách vận chuyển",
    "content": "Detailed shipping policy content...",
    "type": "shipping",
    "createdAt": "2025-01-24T10:00:00.000Z",
    "updatedAt": "2025-01-24T10:00:00.000Z"
  }
}
```

### 4. Get Policies by Type
**GET** `/api/shop-policies/type/:type`

Get all policies of a specific type.

#### Example Request
```bash
GET /api/shop-policies/type/shipping
```

#### Example Response
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "Chính sách vận chuyển",
      "content": "Detailed shipping policy content...",
      "type": "shipping",
      "createdAt": "2025-01-24T10:00:00.000Z",
      "updatedAt": "2025-01-24T10:00:00.000Z"
    }
  ],
  "count": 1
}
```

### 5. Create Policy
**POST** `/api/shop-policies`

Create a new shop policy.

#### Required Fields
- `title`: String - Policy title
- `content`: String - Policy content
- `type`: String - Policy type (must be one of the valid types)

#### Example Request
```bash
POST /api/shop-policies
Content-Type: application/json

{
  "title": "Chính sách bảo hành",
  "content": "Detailed warranty policy content...",
  "type": "warranty"
}
```

#### Example Response
```json
{
  "success": true,
  "message": "Shop policy created successfully",
  "data": {
    "id": 2,
    "title": "Chính sách bảo hành",
    "content": "Detailed warranty policy content...",
    "type": "warranty",
    "createdAt": "2025-01-24T10:00:00.000Z",
    "updatedAt": "2025-01-24T10:00:00.000Z"
  }
}
```

### 6. Update Policy (Full Update)
**PUT** `/api/shop-policies/:id`

Update all fields of a shop policy.

#### Example Request
```bash
PUT /api/shop-policies/1
Content-Type: application/json

{
  "title": "Updated Shipping Policy",
  "content": "Updated shipping policy content...",
  "type": "shipping"
}
```

### 7. Partial Update Policy
**PATCH** `/api/shop-policies/:id`

Update only specific fields of a shop policy.

#### Example Request
```bash
PATCH /api/shop-policies/1
Content-Type: application/json

{
  "title": "Updated Title Only",
  "content": "Updated content only"
}
```

### 8. Delete Policy
**DELETE** `/api/shop-policies/:id`

Delete a shop policy by ID.

#### Example Request
```bash
DELETE /api/shop-policies/1
```

#### Example Response
```json
{
  "success": true,
  "message": "Shop policy deleted successfully"
}
```

### 9. Bulk Import Policies
**POST** `/api/shop-policies/bulk-import`

Import multiple shop policies at once.

#### Example Request
```bash
POST /api/shop-policies/bulk-import
Content-Type: application/json

{
  "policies": [
    {
      "title": "Policy 1",
      "content": "Content 1...",
      "type": "shipping"
    },
    {
      "title": "Policy 2", 
      "content": "Content 2...",
      "type": "payment"
    }
  ]
}
```

#### Example Response
```json
{
  "success": true,
  "message": "Bulk import completed. 2 policies created successfully.",
  "data": {
    "created": [
      // Array of created policies
    ],
    "errors": [
      // Array of any errors that occurred
    ]
  }
}
```

### 10. Delete Policies by Type
**DELETE** `/api/shop-policies/type/:type`

Delete all policies of a specific type.

#### Example Request
```bash
DELETE /api/shop-policies/type/warranty
```

#### Example Response
```json
{
  "success": true,
  "message": "Successfully deleted 3 policies of type: warranty",
  "deletedCount": 3
}
```

## Error Responses

All endpoints return error responses in the following format:

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error message (in development)"
}
```

Common HTTP status codes:
- `200`: Success
- `201`: Created
- `400`: Bad Request (validation errors, invalid policy type)
- `404`: Not Found
- `500`: Internal Server Error

## Validation Rules

### Policy Type Validation
- Must be one of the valid types: `shipping`, `promotion`, `payment`, `return`, `privacy`, `terms`, `warranty`, `refund`
- Case insensitive (automatically converted to lowercase)

### Required Fields
- `title`: Cannot be empty, automatically trimmed
- `content`: Cannot be empty, automatically trimmed  
- `type`: Must be a valid policy type

## Data Import Script

To import sample shop policies data, run:

```bash
node scripts/import-shop-policies.js
```

This script will:
- Create sample policies for different types
- Skip policies that already exist
- Show import summary with statistics

## Testing

To test the Shop Policies API, run:

```bash
node scripts/test-shop-policies-api.js
```

This will run comprehensive tests covering all endpoints and edge cases.
