const express = require('express');
const { PrismaClient } = require('@prisma/client');
const router = express.Router();

const prisma = new PrismaClient();

// Valid policy types
const VALID_POLICY_TYPES = ['shipping', 'promotion', 'payment', 'return', 'privacy', 'terms', 'warranty', 'refund'];

// GET /api/shop-policies - Get all shop policies with optional filtering
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, type, search, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    // Build where clause
    const where = {};
    if (type) {
      where.type = type;
    }
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { content: { contains: search, mode: 'insensitive' } },
        { type: { contains: search, mode: 'insensitive' } }
      ];
    }

    const orderBy = { [sortBy]: sortOrder };

    const [policies, total] = await Promise.all([
      prisma.shopPolicy.findMany({
        where,
        skip,
        take: parseInt(limit),
        orderBy
      }),
      prisma.shopPolicy.count({ where })
    ]);

    res.json({
      success: true,
      data: policies,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Error fetching shop policies:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// GET /api/shop-policies/types - Get all available policy types
router.get('/types', async (req, res) => {
  try {
    // Get unique types from database
    const dbTypes = await prisma.shopPolicy.findMany({
      select: { type: true },
      distinct: ['type']
    });

    const usedTypes = dbTypes.map(p => p.type);
    
    res.json({
      success: true,
      data: {
        validTypes: VALID_POLICY_TYPES,
        usedTypes: usedTypes,
        availableTypes: VALID_POLICY_TYPES.filter(type => !usedTypes.includes(type))
      }
    });
  } catch (error) {
    console.error('Error fetching policy types:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// GET /api/shop-policies/:id - Get a single shop policy by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const policy = await prisma.shopPolicy.findUnique({
      where: { id: parseInt(id) }
    });

    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Shop policy not found'
      });
    }

    res.json({
      success: true,
      data: policy
    });
  } catch (error) {
    console.error('Error fetching shop policy:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// GET /api/shop-policies/type/:type - Get policies by type
router.get('/type/:type', async (req, res) => {
  try {
    const { type } = req.params;
    
    const policies = await prisma.shopPolicy.findMany({
      where: { type: type },
      orderBy: { createdAt: 'desc' }
    });

    res.json({
      success: true,
      data: policies,
      count: policies.length
    });
  } catch (error) {
    console.error('Error fetching policies by type:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// POST /api/shop-policies - Create a new shop policy
router.post('/', async (req, res) => {
  try {
    const { title, content, type } = req.body;

    // Validation
    if (!title || !content || !type) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: title, content, type'
      });
    }

    // Validate policy type
    if (!VALID_POLICY_TYPES.includes(type)) {
      return res.status(400).json({
        success: false,
        message: `Invalid policy type. Valid types are: ${VALID_POLICY_TYPES.join(', ')}`
      });
    }

    const policy = await prisma.shopPolicy.create({
      data: {
        title: title.trim(),
        content: content.trim(),
        type: type.toLowerCase()
      }
    });

    res.status(201).json({
      success: true,
      message: 'Shop policy created successfully',
      data: policy
    });
  } catch (error) {
    console.error('Error creating shop policy:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// PUT /api/shop-policies/:id - Update a shop policy (full update)
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { title, content, type } = req.body;

    // Check if policy exists
    const existingPolicy = await prisma.shopPolicy.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingPolicy) {
      return res.status(404).json({
        success: false,
        message: 'Shop policy not found'
      });
    }

    // Validation
    if (!title || !content || !type) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: title, content, type'
      });
    }

    // Validate policy type
    if (!VALID_POLICY_TYPES.includes(type)) {
      return res.status(400).json({
        success: false,
        message: `Invalid policy type. Valid types are: ${VALID_POLICY_TYPES.join(', ')}`
      });
    }

    const updatedPolicy = await prisma.shopPolicy.update({
      where: { id: parseInt(id) },
      data: {
        title: title.trim(),
        content: content.trim(),
        type: type.toLowerCase()
      }
    });

    res.json({
      success: true,
      message: 'Shop policy updated successfully',
      data: updatedPolicy
    });
  } catch (error) {
    console.error('Error updating shop policy:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// PATCH /api/shop-policies/:id - Partially update a shop policy
router.patch('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updateFields = req.body;

    // Check if policy exists
    const existingPolicy = await prisma.shopPolicy.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingPolicy) {
      return res.status(404).json({
        success: false,
        message: 'Shop policy not found'
      });
    }

    // Prepare update data (only include provided fields)
    const updateData = {};
    
    if (updateFields.title !== undefined) {
      updateData.title = updateFields.title.trim();
    }
    if (updateFields.content !== undefined) {
      updateData.content = updateFields.content.trim();
    }
    if (updateFields.type !== undefined) {
      // Validate policy type if provided
      if (!VALID_POLICY_TYPES.includes(updateFields.type)) {
        return res.status(400).json({
          success: false,
          message: `Invalid policy type. Valid types are: ${VALID_POLICY_TYPES.join(', ')}`
        });
      }
      updateData.type = updateFields.type.toLowerCase();
    }

    const updatedPolicy = await prisma.shopPolicy.update({
      where: { id: parseInt(id) },
      data: updateData
    });

    res.json({
      success: true,
      message: 'Shop policy updated successfully',
      data: updatedPolicy
    });
  } catch (error) {
    console.error('Error updating shop policy:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// DELETE /api/shop-policies/:id - Delete a shop policy
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if policy exists
    const existingPolicy = await prisma.shopPolicy.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingPolicy) {
      return res.status(404).json({
        success: false,
        message: 'Shop policy not found'
      });
    }

    await prisma.shopPolicy.delete({
      where: { id: parseInt(id) }
    });

    res.json({
      success: true,
      message: 'Shop policy deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting shop policy:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// POST /api/shop-policies/bulk-import - Import multiple shop policies
router.post('/bulk-import', async (req, res) => {
  try {
    const { policies } = req.body;

    if (!Array.isArray(policies) || policies.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Policies array is required and must not be empty'
      });
    }

    const createdPolicies = [];
    const errors = [];

    for (let i = 0; i < policies.length; i++) {
      try {
        const policyData = policies[i];
        const { title, content, type } = policyData;

        // Validation for each policy
        if (!title || !content || !type) {
          errors.push({
            index: i,
            message: 'Missing required fields: title, content, type'
          });
          continue;
        }

        // Validate policy type
        if (!VALID_POLICY_TYPES.includes(type)) {
          errors.push({
            index: i,
            message: `Invalid policy type: ${type}. Valid types are: ${VALID_POLICY_TYPES.join(', ')}`
          });
          continue;
        }

        const policy = await prisma.shopPolicy.create({
          data: {
            title: title.trim(),
            content: content.trim(),
            type: type.toLowerCase()
          }
        });

        createdPolicies.push(policy);
      } catch (error) {
        errors.push({
          index: i,
          message: error.message
        });
      }
    }

    res.status(201).json({
      success: true,
      message: `Bulk import completed. ${createdPolicies.length} policies created successfully.`,
      data: {
        created: createdPolicies,
        errors: errors
      }
    });
  } catch (error) {
    console.error('Error in bulk import:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// DELETE /api/shop-policies/type/:type - Delete all policies of a specific type
router.delete('/type/:type', async (req, res) => {
  try {
    const { type } = req.params;

    // Get count of policies to be deleted
    const count = await prisma.shopPolicy.count({
      where: { type: type }
    });

    if (count === 0) {
      return res.status(404).json({
        success: false,
        message: `No policies found with type: ${type}`
      });
    }

    // Delete all policies of this type
    const result = await prisma.shopPolicy.deleteMany({
      where: { type: type }
    });

    res.json({
      success: true,
      message: `Successfully deleted ${result.count} policies of type: ${type}`,
      deletedCount: result.count
    });
  } catch (error) {
    console.error('Error deleting policies by type:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

module.exports = router;
