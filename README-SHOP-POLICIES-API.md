# Shop Policies CRUD API

This document describes the Shop Policies CRUD API that manages store policies like shipping, payment, return policies, etc.

## 🚀 Quick Start

### 1. Install Dependencies
```bash
npm install
# or
yarn install
```

### 2. Setup Database
Make sure your PostgreSQL database is running and the `DATABASE_URL` is configured in your `.env` file.

```bash
# Run Prisma migrations
npx prisma migrate dev
```

### 3. Import Sample Data
```bash
npm run import-shop-policies
```

### 4. Start the Server
```bash
npm start
```

The server will start on `http://localhost:3000`

### 5. Test the API
```bash
npm run test-shop-policies-api
```

## 📋 API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/shop-policies` | Get all policies (with pagination, search, filtering) |
| GET | `/api/shop-policies/types` | Get available policy types |
| GET | `/api/shop-policies/:id` | Get a single policy by ID |
| GET | `/api/shop-policies/type/:type` | Get all policies of a specific type |
| POST | `/api/shop-policies` | Create a new policy |
| PUT | `/api/shop-policies/:id` | Update a policy (full update) |
| PATCH | `/api/shop-policies/:id` | Partially update a policy |
| DELETE | `/api/shop-policies/:id` | Delete a policy |
| POST | `/api/shop-policies/bulk-import` | Import multiple policies |
| DELETE | `/api/shop-policies/type/:type` | Delete all policies of a specific type |

## 📊 Data Structure

The API handles shop policies with the following structure:

```json
{
  "id": 1,
  "title": "Chính sách vận chuyển",
  "content": "Detailed policy content...",
  "type": "shipping",
  "createdAt": "2025-01-24T10:00:00.000Z",
  "updatedAt": "2025-01-24T10:00:00.000Z"
}
```

## 🏷️ Policy Types

The API supports these policy types:
- **shipping** - Shipping and delivery policies
- **promotion** - Promotion and discount policies
- **payment** - Payment method policies
- **return** - Return and exchange policies
- **privacy** - Privacy and data protection policies
- **terms** - Terms of service
- **warranty** - Product warranty policies
- **refund** - Refund policies

## 🔍 Features

### Pagination
```bash
GET /api/shop-policies?page=1&limit=10
```

### Search
```bash
GET /api/shop-policies?search=vận chuyển
```

### Filter by Type
```bash
GET /api/shop-policies?type=shipping
```

### Sorting
```bash
GET /api/shop-policies?sortBy=title&sortOrder=asc
```

### Combined Query
```bash
GET /api/shop-policies?page=1&limit=5&type=shipping&search=giao&sortBy=createdAt&sortOrder=desc
```

## 📝 Example Usage

### Create a Policy
```bash
curl -X POST http://localhost:3000/api/shop-policies \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Chính sách bảo hành",
    "content": "Sản phẩm được bảo hành 12 tháng...",
    "type": "warranty"
  }'
```

### Get Policies by Type
```bash
curl -X GET http://localhost:3000/api/shop-policies/type/shipping
```

### Update a Policy
```bash
curl -X PATCH http://localhost:3000/api/shop-policies/1 \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Updated Policy Title",
    "content": "Updated policy content..."
  }'
```

### Delete a Policy
```bash
curl -X DELETE http://localhost:3000/api/shop-policies/1
```

## 🛠️ Scripts

- `npm run import-shop-policies` - Import sample policy data
- `npm run test-shop-policies-api` - Run API tests
- `npm start` - Start the server

## 📁 File Structure

```
├── routes/
│   └── shop-policies.js        # Shop Policies API routes
├── scripts/
│   ├── import-shop-policies.js # Data import script
│   └── test-shop-policies-api.js # API testing script
├── docs/
│   └── shop-policies-api.md    # Detailed API documentation
└── prisma/
    └── schema.prisma           # Database schema
```

## 🔧 Database Schema

The API uses the ShopPolicy Prisma model:

```prisma
model ShopPolicy {
  id        Int      @id @default(autoincrement())
  title     String
  content   String
  type      String   // "shipping", "promotion", "payment", "return"...
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
```

## 🚨 Error Handling

All endpoints return consistent error responses:

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error message"
}
```

### Validation Errors
- **400 Bad Request**: Missing required fields, invalid policy type
- **404 Not Found**: Policy not found
- **500 Internal Server Error**: Server errors

### Policy Type Validation
The API validates that policy types are one of the allowed values:
`shipping`, `promotion`, `payment`, `return`, `privacy`, `terms`, `warranty`, `refund`

## 📖 Full Documentation

For detailed API documentation with request/response examples, see `docs/shop-policies-api.md`.

## 🧪 Testing

The API includes comprehensive tests that cover:
- Creating policies
- Retrieving policies (single, multiple, by type)
- Updating policies (full and partial)
- Deleting policies
- Bulk importing
- Search and filtering
- Policy type validation

Run tests with: `npm run test-shop-policies-api`

## 🔄 Sample Data

The import script includes sample Vietnamese policies for:
- Shipping policies (Chính sách vận chuyển)
- Promotion policies (Chính sách khuyến mãi)
- Payment policies (Chính sách thanh toán)
- Return policies (Chính sách đổi trả)
- Privacy policies (Chính sách bảo mật)
- Terms of service (Điều khoản sử dụng)

## 🤝 Contributing

When adding new features:
1. Update the Prisma schema if needed
2. Add corresponding API endpoints
3. Update documentation
4. Add tests
5. Update the import script if data structure changes

## 🌐 Integration

This API can be easily integrated with:
- E-commerce websites
- Mobile applications
- Admin dashboards
- Customer service systems

The policies can be displayed to customers during checkout, in help sections, or in legal pages.
