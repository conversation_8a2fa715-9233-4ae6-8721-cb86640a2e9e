const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/shop-policies';

// Test data
const testPolicy = {
  title: "Test Policy",
  content: "This is a test policy content with detailed information about our test policy.",
  type: "warranty"
};

async function testShopPoliciesAPI() {
  console.log('🧪 Starting Shop Policies API Tests...\n');

  try {
    // Test 1: Get policy types
    console.log('1️⃣ Testing GET policy types...');
    const typesResponse = await axios.get(`${BASE_URL}/types`);
    console.log('✅ GET TYPES Success:', `Found ${typesResponse.data.data.validTypes.length} valid types`);
    console.log(`   Valid types: ${typesResponse.data.data.validTypes.join(', ')}`);
    console.log(`   Used types: ${typesResponse.data.data.usedTypes.join(', ') || 'None'}\n`);

    // Test 2: Create a policy
    console.log('2️⃣ Testing CREATE policy...');
    const createResponse = await axios.post(BASE_URL, testPolicy);
    console.log('✅ CREATE Success:', createResponse.data.message);
    const createdPolicyId = createResponse.data.data.id;
    console.log(`   Created policy ID: ${createdPolicyId}\n`);

    // Test 3: Get all policies
    console.log('3️⃣ Testing GET all policies...');
    const getAllResponse = await axios.get(BASE_URL);
    console.log('✅ GET ALL Success:', `Found ${getAllResponse.data.data.length} policies`);
    console.log(`   Total policies: ${getAllResponse.data.pagination.total}\n`);

    // Test 4: Get single policy
    console.log('4️⃣ Testing GET single policy...');
    const getSingleResponse = await axios.get(`${BASE_URL}/${createdPolicyId}`);
    console.log('✅ GET SINGLE Success:', getSingleResponse.data.data.title);
    console.log(`   Policy type: ${getSingleResponse.data.data.type}\n`);

    // Test 5: Get policies by type
    console.log('5️⃣ Testing GET policies by type...');
    const getByTypeResponse = await axios.get(`${BASE_URL}/type/${testPolicy.type}`);
    console.log('✅ GET BY TYPE Success:', `Found ${getByTypeResponse.data.count} policies of type "${testPolicy.type}"`);
    console.log(`   First policy: ${getByTypeResponse.data.data[0]?.title || 'None'}\n`);

    // Test 6: Update policy (PATCH)
    console.log('6️⃣ Testing PATCH policy...');
    const patchData = {
      title: "Updated Test Policy",
      content: "This is updated content for the test policy."
    };
    const patchResponse = await axios.patch(`${BASE_URL}/${createdPolicyId}`, patchData);
    console.log('✅ PATCH Success:', patchResponse.data.message);
    console.log(`   Updated title: ${patchResponse.data.data.title}\n`);

    // Test 7: Search policies
    console.log('7️⃣ Testing SEARCH policies...');
    const searchResponse = await axios.get(`${BASE_URL}?search=Updated&limit=5`);
    console.log('✅ SEARCH Success:', `Found ${searchResponse.data.data.length} policies matching "Updated"`);
    console.log(`   First result: ${searchResponse.data.data[0]?.title || 'None'}\n`);

    // Test 8: Filter by type
    console.log('8️⃣ Testing FILTER by type...');
    const filterResponse = await axios.get(`${BASE_URL}?type=${testPolicy.type}&limit=10`);
    console.log('✅ FILTER Success:', `Found ${filterResponse.data.data.length} policies of type "${testPolicy.type}"`);
    console.log(`   Total matching: ${filterResponse.data.pagination.total}\n`);

    // Test 9: Bulk import
    console.log('9️⃣ Testing BULK IMPORT...');
    const bulkData = {
      policies: [
        { ...testPolicy, title: "Bulk Test Policy 1", type: "refund" },
        { ...testPolicy, title: "Bulk Test Policy 2", type: "warranty" }
      ]
    };
    const bulkResponse = await axios.post(`${BASE_URL}/bulk-import`, bulkData);
    console.log('✅ BULK IMPORT Success:', bulkResponse.data.message);
    console.log(`   Created ${bulkResponse.data.data.created.length} policies\n`);

    // Test 10: Delete policy
    console.log('🔟 Testing DELETE policy...');
    const deleteResponse = await axios.delete(`${BASE_URL}/${createdPolicyId}`);
    console.log('✅ DELETE Success:', deleteResponse.data.message);

    // Test 11: Verify deletion
    console.log('1️⃣1️⃣ Testing GET deleted policy (should fail)...');
    try {
      await axios.get(`${BASE_URL}/${createdPolicyId}`);
      console.log('❌ DELETE verification failed - policy still exists');
    } catch (error) {
      if (error.response && error.response.status === 404) {
        console.log('✅ DELETE verified - policy not found (as expected)\n');
      } else {
        throw error;
      }
    }

    // Test 12: Test invalid policy type
    console.log('1️⃣2️⃣ Testing INVALID policy type...');
    try {
      await axios.post(BASE_URL, {
        title: "Invalid Policy",
        content: "Test content",
        type: "invalid_type"
      });
      console.log('❌ Invalid type test failed - should have been rejected');
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log('✅ Invalid type properly rejected:', error.response.data.message);
      } else {
        throw error;
      }
    }

    console.log('\n🎉 All Shop Policies API tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response?.data) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// Check if server is running first
async function checkServer() {
  try {
    await axios.get('http://localhost:3000');
    return true;
  } catch (error) {
    return false;
  }
}

async function main() {
  console.log('🔍 Checking if server is running...');
  const serverRunning = await checkServer();
  
  if (!serverRunning) {
    console.log('❌ Server is not running on http://localhost:3000');
    console.log('   Please start the server with: npm start');
    return;
  }
  
  console.log('✅ Server is running\n');
  await testShopPoliciesAPI();
}

// Run the tests
main();
