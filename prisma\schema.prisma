// schema.prisma

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Product {
  id           Int           @id @default(autoincrement())
  name         String       // Tên sản phẩm
  price        Float          // Giá sản phẩm
  imageUrls    String[]      // array URL ảnh sản phẩm
  ingredients  String       // Thành phần
  instructions String        // Cách chế biến
  benefits     String        // Lợi ích
  usage        String        // Cách sử dụng
  notes        String?      // Lưu ý
  otherInfo    String?      // Thông tin khác
  targetUser   String?      // mục đích sử dụng
  purpose      String?      // mục đích sản phẩm
  faqs         ProductFAQ[]   // Câu hỏi thường gặp
  description  String?      // Mô tả sản phẩm
  orderItems   OrderItem[]    // Items in orders
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
}

model ProductFAQ {
  id         Int      @id @default(autoincrement())
  product    Product  @relation(fields: [productId], references: [id])
  productId  Int
  question   String
  answer     String
}

model ShopPolicy {
  id       Int    @id @default(autoincrement())
  title    String
  content  String
  type     String // "shipping", "promotion", "payment", "return"...
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
}

model MenuImage {
  id        Int    @id @default(autoincrement())
  imageUrl  String
  title     String
  order     Int?
}

model Customer {
  id          Int      @id @default(autoincrement())
  fbId        String?   @unique // Facebook user id (optional)
  name        String?
  avatar      String?
  lastMessage DateTime?
  orders      Order[]
}

model User {
  id        Int      @id @default(autoincrement())
  email     String  @unique
  password  String
  isActive  Boolean @default(true)
  lastLogin DateTime?
  role      String  @default("customer") // customer, admin, ...
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Order {
  id           Int        @id @default(autoincrement())
  customer     Customer   @relation(fields: [customerId], references: [id])
  customerId   Int
  items        OrderItem[]
  status       String     // pending, paid, shipping, done, canceled
  totalAmount  Float
  createdAt    DateTime   @default(now())
  note         String?
}

model OrderItem {
  id         Int      @id @default(autoincrement())
  order      Order    @relation(fields: [orderId], references: [id])
  orderId    Int
  product    Product  @relation(fields: [productId], references: [id])
  productId  Int
  quantity   Int
  price      Float
}
