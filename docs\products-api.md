# Products API Documentation

## Base URL
```
http://localhost:3000/api/products
```

## Endpoints

### 1. Get All Products
**GET** `/api/products`

Get a paginated list of all products with optional search and sorting.

#### Query Parameters
- `page` (optional): Page number (default: 1)
- `limit` (optional): Number of items per page (default: 10)
- `search` (optional): Search term to filter products by name, ingredients, or description
- `sortBy` (optional): Field to sort by (default: 'createdAt')
- `sortOrder` (optional): Sort order 'asc' or 'desc' (default: 'desc')

#### Example Request
```bash
GET /api/products?page=1&limit=5&search=heo&sortBy=name&sortOrder=asc
```

#### Example Response
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Gi<PERSON> heo hun khói",
      "price": 20,
      "imageUrls": ["https://example.com/image1.jpg"],
      "ingredients": "<PERSON><PERSON><PERSON> heo nguy<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> sọ...",
      "instructions": "<PERSON><PERSON><PERSON> mát tủ lạnh: 7–10 ngày...",
      "benefits": "Cung cấp protein cao...",
      "usage": "Rã đông tự nhiên 30 phút...",
      "notes": "Chân giò heo trước hoặc sau...",
      "otherInfo": "Không tái cấp đông sau khi rã đông...",
      "targetUser": "Phù hợp mọi lứa tuổi...",
      "purpose": "Làm món ăn vặt, ăn chơi...",
      "description": "Không tái cấp đông sau khi rã đông...",
      "createdAt": "2025-01-24T10:00:00.000Z",
      "updatedAt": "2025-01-24T10:00:00.000Z",
      "faqs": [
        {
          "id": 1,
          "productId": 1,
          "question": "Giò heo hun khói là gì?",
          "answer": "Là món ăn làm từ chân giò heo..."
        }
      ]
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 5,
    "total": 1,
    "pages": 1
  }
}
```

### 2. Get Single Product
**GET** `/api/products/:id`

Get a specific product by ID.

#### Example Request
```bash
GET /api/products/1
```

#### Example Response
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "Giò heo hun khói",
    "price": 20,
    "imageUrls": ["https://example.com/image1.jpg"],
    "ingredients": "Giò heo nguyên chiếc, Muối hạt, Tiêu sọ...",
    "instructions": "Ngăn mát tủ lạnh: 7–10 ngày...",
    "benefits": "Cung cấp protein cao...",
    "usage": "Rã đông tự nhiên 30 phút...",
    "notes": "Chân giò heo trước hoặc sau...",
    "otherInfo": "Không tái cấp đông sau khi rã đông...",
    "targetUser": "Phù hợp mọi lứa tuổi...",
    "purpose": "Làm món ăn vặt, ăn chơi...",
    "description": "Không tái cấp đông sau khi rã đông...",
    "createdAt": "2025-01-24T10:00:00.000Z",
    "updatedAt": "2025-01-24T10:00:00.000Z",
    "faqs": [
      {
        "id": 1,
        "productId": 1,
        "question": "Giò heo hun khói là gì?",
        "answer": "Là món ăn làm từ chân giò heo..."
      }
    ]
  }
}
```

### 3. Create Product
**POST** `/api/products`

Create a new product.

#### Required Fields
- `product_name`: String
- `price`: Number or String (e.g., "20$")
- `ingredients`: String
- `instructions`: String
- `benefits`: String
- `usage`: String

#### Optional Fields
- `images`: Array of image URLs
- `notes`: String
- `otherInfo`: String
- `targetUser`: String
- `purpose`: String
- `description`: String
- `faqs`: Array of FAQ objects with `question` and `answer`

#### Example Request
```bash
POST /api/products
Content-Type: application/json

{
  "product_name": "Giò heo hun khói",
  "price": "20$",
  "images": ["https://example.com/image1.jpg"],
  "ingredients": "Giò heo nguyên chiếc, Muối hạt, Tiêu sọ...",
  "instructions": "Ngăn mát tủ lạnh: 7–10 ngày...",
  "benefits": "Cung cấp protein cao...",
  "usage": "Rã đông tự nhiên 30 phút...",
  "notes": "Chân giò heo trước hoặc sau...",
  "otherInfo": "Không tái cấp đông sau khi rã đông...",
  "targetUser": "Phù hợp mọi lứa tuổi...",
  "purpose": "Làm món ăn vặt, ăn chơi...",
  "description": "Không tái cấp đông sau khi rã đông...",
  "faqs": [
    {
      "question": "Giò heo hun khói là gì?",
      "answer": "Là món ăn làm từ chân giò heo..."
    }
  ]
}
```

#### Example Response
```json
{
  "success": true,
  "message": "Product created successfully",
  "data": {
    "id": 1,
    "name": "Giò heo hun khói",
    "price": 20,
    // ... other fields
  }
}
```

### 4. Update Product (Full Update)
**PUT** `/api/products/:id`

Update all fields of a product.

#### Example Request
```bash
PUT /api/products/1
Content-Type: application/json

{
  "product_name": "Updated Product Name",
  "price": 25,
  "images": ["https://example.com/new-image.jpg"],
  "ingredients": "Updated ingredients...",
  "instructions": "Updated instructions...",
  "benefits": "Updated benefits...",
  "usage": "Updated usage...",
  "notes": "Updated notes...",
  "otherInfo": "Updated other info...",
  "targetUser": "Updated target user...",
  "purpose": "Updated purpose...",
  "description": "Updated description...",
  "faqs": [
    {
      "question": "Updated question?",
      "answer": "Updated answer..."
    }
  ]
}
```

### 5. Partial Update Product
**PATCH** `/api/products/:id`

Update only specific fields of a product.

#### Example Request
```bash
PATCH /api/products/1
Content-Type: application/json

{
  "price": 25,
  "description": "Updated description only"
}
```

### 6. Deactivate Product (Soft Delete)
**PUT** `/api/products/:id/deactivate`

Deactivate a product instead of permanently deleting it. This is useful when the product is referenced in orders.

#### Example Request
```bash
PUT /api/products/1/deactivate
```

#### Example Response
```json
{
  "success": true,
  "message": "Product deactivated successfully",
  "data": {
    "id": 1,
    "name": "Product Name",
    "notes": "Original notes\n\n[DEACTIVATED: 2025-01-24T10:00:00.000Z]",
    // ... other fields
  }
}
```

### 7. Check Delete Eligibility
**GET** `/api/products/:id/delete-check`

Check if a product can be safely deleted and see what's preventing deletion.

#### Example Request
```bash
GET /api/products/1/delete-check
```

#### Example Response (Can Delete)
```json
{
  "success": true,
  "data": {
    "productId": 1,
    "productName": "Test Product",
    "canDelete": true,
    "blockingReasons": [],
    "relatedData": {
      "orderItems": 0,
      "faqs": 2,
      "orders": []
    },
    "recommendations": ["Product can be safely deleted"]
  }
}
```

#### Example Response (Cannot Delete)
```json
{
  "success": true,
  "data": {
    "productId": 1,
    "productName": "Test Product",
    "canDelete": false,
    "blockingReasons": ["Referenced in existing orders"],
    "relatedData": {
      "orderItems": 2,
      "faqs": 1,
      "orders": [
        {
          "orderId": 5,
          "orderStatus": "pending",
          "orderDate": "2025-01-24T10:00:00.000Z",
          "quantity": 2
        }
      ]
    },
    "recommendations": ["Use deactivate endpoint instead", "Or resolve order references first"]
  }
}
```

### 8. Delete Product
**DELETE** `/api/products/:id`

Permanently delete a product by ID. This will fail if the product is referenced in any orders.

#### Example Request
```bash
DELETE /api/products/1
```

#### Example Response (Success)
```json
{
  "success": true,
  "message": "Product deleted successfully"
}
```

#### Example Response (Foreign Key Constraint)
```json
{
  "success": false,
  "message": "Cannot delete product because it is referenced in existing orders",
  "details": "Product is used in 3 order item(s)"
}
```

### 9. Bulk Import Products
**POST** `/api/products/bulk-import`

Import multiple products at once.

#### Example Request
```bash
POST /api/products/bulk-import
Content-Type: application/json

{
  "products": [
    {
      "product_name": "Product 1",
      "price": "20$",
      "ingredients": "Ingredients 1...",
      "instructions": "Instructions 1...",
      "benefits": "Benefits 1...",
      "usage": "Usage 1...",
      "faqs": []
    },
    {
      "product_name": "Product 2",
      "price": "30$",
      "ingredients": "Ingredients 2...",
      "instructions": "Instructions 2...",
      "benefits": "Benefits 2...",
      "usage": "Usage 2...",
      "faqs": []
    }
  ]
}
```

#### Example Response
```json
{
  "success": true,
  "message": "Bulk import completed. 2 products created successfully.",
  "data": {
    "created": [
      // Array of created products
    ],
    "errors": [
      // Array of any errors that occurred
    ]
  }
}
```

## Error Responses

All endpoints return error responses in the following format:

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error message (in development)"
}
```

Common HTTP status codes:
- `200`: Success
- `201`: Created
- `400`: Bad Request (validation errors)
- `404`: Not Found
- `500`: Internal Server Error

## Data Import Script

To import the sample data from `data/products_only.json`, run:

```bash
node scripts/import-products.js
```

This script will:
- Read the JSON file
- Parse the product data
- Create products in the database
- Skip products that already exist
- Create associated FAQs for each product
