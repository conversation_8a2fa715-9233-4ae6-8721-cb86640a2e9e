{"name": "backend", "version": "0.0.0", "private": true, "scripts": {"start": "node ./bin/www", "import-products": "node scripts/import-products.js", "import-shop-policies": "node scripts/import-shop-policies.js", "test-api": "node scripts/test-api.js", "test-shop-policies-api": "node scripts/test-shop-policies-api.js"}, "dependencies": {"@prisma/client": "^6.12.0", "cookie-parser": "~1.4.4", "cors": "^2.8.5", "debug": "~2.6.9", "express": "~4.16.1", "morgan": "~1.9.1"}, "devDependencies": {"@prisma/client": "^6.12.0", "axios": "^1.6.0", "prisma": "^6.12.0"}}